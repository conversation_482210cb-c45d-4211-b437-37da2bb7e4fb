const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const scheduleService = require('../../../../services/scheduleService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const DateUtils = require('../../../../utils/dateUtils');

/**
 * API lấy danh sách lịch làm việc được nhóm theo user
 * POST /api/v1.0/work-schedule/list
 *
 * Tr<PERSON> về dữ liệu được tổ chức theo từng user với:
 * - Thông tin user (name, idNumber, units)
 * - Danh sách lịch làm việc của user đó
 * - Thống kê tổng quan (tổng số schedules, users, trung bình schedules/user)
 * - Pagination được điều chỉnh phù hợp với việc nhóm theo user
 */
module.exports = (req, res) => {
  const viewerId = req.user.id;
  const {
    startDate,
    endDate,
    userId,
    unitId,
    page = 1,
    limit = 20
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      // Chỉ chấp nhận định dạng DD-MM-YYYY
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      userId: Joi.objectId().optional(),
      unitId: Joi.objectId().optional(),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20)
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate nếu có cả hai (định dạng DD-MM-YYYY)
    if (startDate && endDate) {
      if (DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.ATTENDANCE.WRONG_DATE
        });
      }
    }

    next();
  };

  const getScheduleList = (next) => {
    try {
      const filters = {
        startDate,
        endDate,
        userId,
        unitId,
        page,
        limit
      };

      scheduleService.getScheduleList(viewerId, filters)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  /**
   * Nhóm dữ liệu lịch làm việc theo user
   * @param {Array} schedules - Danh sách lịch làm việc
   * @returns {Array} Dữ liệu được nhóm theo user
   */
  const groupSchedulesByUser = (schedules) => {
    // Nhóm schedules theo user._id
    const groupedByUser = _.groupBy(schedules, (schedule) => {
      return schedule.user._id.toString();
    });

    // Chuyển đổi thành array với thông tin user và danh sách schedules
    const userSchedules = Object.keys(groupedByUser).map(userId => {
      const userScheduleList = groupedByUser[userId];
      const userInfo = userScheduleList[0].user; // Lấy thông tin user từ schedule đầu tiên

      return {
        user: {
          _id: userInfo._id,
          name: userInfo.name,
          idNumber: userInfo.idNumber,
          units: userInfo.units
        },
        schedules: userScheduleList.map(schedule => ({
          _id: schedule._id,
          date: schedule.date,
          shifts: schedule.shifts,
          createdBy: schedule.createdBy,
          createdAt: schedule.createdAt,
          updatedAt: schedule.updatedAt,
          status: schedule.status
        })),
        totalSchedules: userScheduleList.length
      };
    });

    // Sắp xếp theo tên user
    return userSchedules.sort((a, b) => {
      return a.user.name.localeCompare(b.user.name, 'vi', { sensitivity: 'base' });
    });
  };

  const formatResponse = (next) => {
    try {
      const { schedules, pagination } = result.data;

      // Nhóm dữ liệu theo user
      const groupedData = groupSchedulesByUser(schedules);

      // Tính toán pagination mới dựa trên số lượng users
      const totalUsers = groupedData.length;
      const usersPerPage = pagination.pages > 0 ? Math.ceil(totalUsers / pagination.pages) : totalUsers;

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách lịch làm việc theo user thành công'
        },
        data: {
          userSchedules: groupedData,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total: pagination.total, // Tổng số schedules
            totalUsers: totalUsers, // Tổng số users
            pages: pagination.pages,
            usersPerPage: usersPerPage
          },
          summary: {
            totalSchedules: pagination.total,
            totalUsers: totalUsers,
            averageSchedulesPerUser: totalUsers > 0 ? Math.round(pagination.total / totalUsers * 100) / 100 : 0
          }
        }
      });
    } catch (error) {
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  async.waterfall([
    validateParams,
    getScheduleList,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};